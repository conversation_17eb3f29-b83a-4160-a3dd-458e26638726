import 'package:my_video/app/config/environment_config.dart';

enum Flavor { dev, prod }

class AppConfig {
  static AppConfig? _instance;
  static AppConfig get instance => _instance!;

  final String appName;
  final Flavor flavor;
  final String environment;

  AppConfig._internal({
    required this.appName,
    required this.flavor,
    required this.environment,
  });

  static void create({
    required String appName,
    required Flavor flavor,
    String environment = 'local',
  }) {
    _instance = AppConfig._internal(
      appName: appName,
      flavor: flavor,
      environment: environment,
    );
  }

  bool get isDev => flavor == Flavor.dev;
  bool get isProd => flavor == Flavor.prod;

  /// Get base URL from environment configuration
  String get baseUrl {
    final serverConfig = EnvironmentConfig.getServerConfig(environment);
    return serverConfig.baseUrl;
  }

  /// Get current server configuration
  ServerConfig get serverConfig =>
      EnvironmentConfig.getServerConfig(environment);

  // Backend expects these specific endpoints
  String get loginEndpoint => 'login';
  String get logoutEndpoint => 'logoutuser';
  String get unregisteredTokenEndpoint => 'unregisteredusertoken';
}
