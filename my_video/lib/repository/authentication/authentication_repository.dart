import 'dart:io';

abstract class AuthenticationRepository {
  Future<Map<String, dynamic>> getUnregisteredUserToken();

  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  });

  Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
    String? lastName,
    String? gender,
    File? profileImage,
  });

  Future<Map<String, dynamic>> forgotPassword({required String email});

  Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String password,
  });

  Future<Map<String, dynamic>> changePassword({
    required String oldPassword,
    required String newPassword,
  });

  Future<Map<String, dynamic>> refreshToken();

  Future<Map<String, dynamic>> logout();

  Future<Map<String, dynamic>> getUserProfile();

  Future<Map<String, dynamic>> updateProfile({
    required Map<String, dynamic> userData,
  });
}
