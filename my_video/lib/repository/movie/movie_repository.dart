import 'package:my_video/app_imports.dart';

abstract class MovieRepository {
  Future<CategoriesResponse> getAllCategories();
  Future<FeaturedMoviesResponse> getFeaturedMovies();

  Future<SearchMoviesResponse> searchMovies(
    String query, {
    int page = 1,
    int perPage = 20,
  });
  Future<MovieModel?> getMovieById(String id);
  Future<List<MovieModel>> getRelatedMovies(String movieId, String category);
}

class MovieRepositoryImpl implements MovieRepository {
  final Logger _logger = Logger();

  @override
  Future<CategoriesResponse> getAllCategories() async {
    try {
      final response = await RestHelper.get('/movies/categories');

      if (response.statusCode == 200) {
        Logger().d(response.body);
        final responseData = jsonDecode(response.body);
        final categoriesResponse = CategoriesResponse.fromJson(responseData);

        if (categoriesResponse.success && categoriesResponse.data.isNotEmpty) {
          await HiveHelper.saveCategories(categoriesResponse.data);
        }

        return categoriesResponse;
      } else {
        throw Exception('Failed to fetch categories: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error fetching categories: $e');

      final cachedCategories = HiveHelper.getAllCategories();
      if (cachedCategories.isNotEmpty) {
        _logger.i('Returning cached categories');
        return CategoriesResponse(
          success: true,
          message: 'Cached data',
          data: cachedCategories,
        );
      }

      rethrow;
    }
  }

  @override
  Future<SearchMoviesResponse> searchMovies(
    String query, {
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final response = await RestHelper.get(
        '/movies/search',
        queryParameters: {
          'q': query,
          'page': page.toString(),
          'per_page': perPage.toString(),
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final searchResponse = SearchMoviesResponse.fromJson(responseData);

        // Cache search results locally
        if (searchResponse.success && searchResponse.data.isNotEmpty) {
          await HiveHelper.saveMovies(searchResponse.data);
        }

        return searchResponse;
      } else {
        throw Exception('Failed to search movies: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error searching movies: $e');

      // Perform local search if available
      final allMovies = HiveHelper.getAllMovies();
      final filteredMovies = allMovies.where((movie) {
        return movie.title.toLowerCase().contains(query.toLowerCase()) ||
            movie.description?.toLowerCase().contains(query.toLowerCase()) ==
                true ||
            movie.category.toLowerCase().contains(query.toLowerCase()) ||
            movie.genreString.toLowerCase().contains(query.toLowerCase());
      }).toList();

      if (filteredMovies.isNotEmpty) {
        _logger.i('Returning local search results for: $query');
        return SearchMoviesResponse(
          success: true,
          message: 'Local search results',
          query: query,
          data: filteredMovies,
          totalCount: filteredMovies.length,
          page: page,
          perPage: perPage,
          hasMore: false,
        );
      }

      rethrow;
    }
  }

  @override
  Future<MovieModel?> getMovieById(String id) async {
    try {
      final response = await RestHelper.get('/movies/$id');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final movieData = responseData['data'];
        if (movieData != null) {
          final movie = MovieModel.fromJson(movieData);

          // Cache movie locally
          await HiveHelper.saveMovie(movie);

          return movie;
        }
      }

      throw Exception('Failed to fetch movie: ${response.statusCode}');
    } catch (e) {
      _logger.e('Error fetching movie by ID: $e');

      // Return cached movie if available
      final cachedMovie = HiveHelper.getMovie(id);
      if (cachedMovie != null) {
        _logger.i('Returning cached movie: $id');
        return cachedMovie;
      }

      rethrow;
    }
  }

  @override
  Future<List<MovieModel>> getRelatedMovies(
    String movieId,
    String category,
  ) async {
    try {
      final response = await RestHelper.get(
        '/movies/$movieId/related',
        queryParameters: {'category': category},
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final List<dynamic> moviesData = responseData['data'] ?? [];
        final relatedMovies = moviesData
            .map((json) => MovieModel.fromJson(json))
            .toList();

        // Cache related movies locally
        if (relatedMovies.isNotEmpty) {
          await HiveHelper.saveMovies(relatedMovies);
        }

        return relatedMovies;
      } else {
        throw Exception(
          'Failed to fetch related movies: ${response.statusCode}',
        );
      }
    } catch (e) {
      _logger.e('Error fetching related movies: $e');

      final cachedMovies = HiveHelper.getMoviesByCategory(
        category,
      ).where((movie) => movie.id != movieId).take(10).toList();

      if (cachedMovies.isNotEmpty) {
        _logger.i('Returning cached related movies for category: $category');
        return cachedMovies;
      }

      return [];
    }
  }

  @override
  Future<FeaturedMoviesResponse> getFeaturedMovies() {
    // TODO: implement getFeaturedMovies
    throw UnimplementedError();
  }
}
